(['D:\\python\\微信客服\\send_message_clean.py'],
 ['D:\\python\\微信客服', 'D:\\python\\微信客服'],
 ['configparser',
  'pymysql',
  'datetime',
  'logging',
  'os',
  'sys',
  'subprocess',
  'ctypes',
  'shutil',
  'time',
  'json',
  'hashlib',
  'hmac',
  'base64',
  'uuid',
  'glob',
  'wxauto',
  'win32gui',
  'urllib',
  'urllib.request',
  'urllib.parse',
  'urllib.error',
  'urllib3',
  'urllib3.util',
  'urllib3.util.retry',
  'urllib3.exceptions',
  'requests',
  'requests.adapters',
  'requests.auth',
  'requests.cookies',
  'requests.exceptions',
  'requests.models',
  'requests.sessions',
  'requests.structures',
  'requests.utils',
  'certifi',
  'charset_normalizer',
  'pyperclip',
  'pyautogui',
  'keyboard',
  'mouse',
  'PIL',
  'PIL.ImageGrab',
  'PIL.Image',
  'PIL.ImageDraw',
  'PIL.ImageFont',
  'psutil',
  'comtypes',
  'comtypes.stream',
  'comtypes.gen',
  'wxauto',
  'wxauto.color',
  'wxauto.elements',
  'wxauto.errors',
  'wxauto.languages',
  'wxauto.uiautomation',
  'wxauto.utils',
  'wxauto.wxauto',
  'PIL',
  'PIL.BdfFontFile',
  'PIL.BlpImagePlugin',
  'PIL.BmpImagePlugin',
  'PIL.BufrStubImagePlugin',
  'PIL.ContainerIO',
  'PIL.CurImagePlugin',
  'PIL.DcxImagePlugin',
  'PIL.DdsImagePlugin',
  'PIL.EpsImagePlugin',
  'PIL.ExifTags',
  'PIL.FitsImagePlugin',
  'PIL.FliImagePlugin',
  'PIL.FontFile',
  'PIL.FpxImagePlugin',
  'PIL.FtexImagePlugin',
  'PIL.GbrImagePlugin',
  'PIL.GdImageFile',
  'PIL.GifImagePlugin',
  'PIL.GimpGradientFile',
  'PIL.GimpPaletteFile',
  'PIL.GribStubImagePlugin',
  'PIL.Hdf5StubImagePlugin',
  'PIL.IcnsImagePlugin',
  'PIL.IcoImagePlugin',
  'PIL.ImImagePlugin',
  'PIL.Image',
  'PIL.ImageChops',
  'PIL.ImageCms',
  'PIL.ImageColor',
  'PIL.ImageDraw',
  'PIL.ImageDraw2',
  'PIL.ImageEnhance',
  'PIL.ImageFile',
  'PIL.ImageFilter',
  'PIL.ImageFont',
  'PIL.ImageGrab',
  'PIL.ImageMath',
  'PIL.ImageMode',
  'PIL.ImageMorph',
  'PIL.ImageOps',
  'PIL.ImagePalette',
  'PIL.ImagePath',
  'PIL.ImageQt',
  'PIL.ImageSequence',
  'PIL.ImageShow',
  'PIL.ImageStat',
  'PIL.ImageTk',
  'PIL.ImageTransform',
  'PIL.ImageWin',
  'PIL.ImtImagePlugin',
  'PIL.IptcImagePlugin',
  'PIL.Jpeg2KImagePlugin',
  'PIL.JpegImagePlugin',
  'PIL.JpegPresets',
  'PIL.McIdasImagePlugin',
  'PIL.MicImagePlugin',
  'PIL.MpegImagePlugin',
  'PIL.MpoImagePlugin',
  'PIL.MspImagePlugin',
  'PIL.PSDraw',
  'PIL.PaletteFile',
  'PIL.PalmImagePlugin',
  'PIL.PcdImagePlugin',
  'PIL.PcfFontFile',
  'PIL.PcxImagePlugin',
  'PIL.PdfImagePlugin',
  'PIL.PdfParser',
  'PIL.PixarImagePlugin',
  'PIL.PngImagePlugin',
  'PIL.PpmImagePlugin',
  'PIL.PsdImagePlugin',
  'PIL.PyAccess',
  'PIL.QoiImagePlugin',
  'PIL.SgiImagePlugin',
  'PIL.SpiderImagePlugin',
  'PIL.SunImagePlugin',
  'PIL.TarIO',
  'PIL.TgaImagePlugin',
  'PIL.TiffImagePlugin',
  'PIL.TiffTags',
  'PIL.WalImageFile',
  'PIL.WebPImagePlugin',
  'PIL.WmfImagePlugin',
  'PIL.XVThumbImagePlugin',
  'PIL.XbmImagePlugin',
  'PIL.XpmImagePlugin',
  'PIL.__main__',
  'PIL._binary',
  'PIL._deprecate',
  'PIL._imaging',
  'PIL._imagingcms',
  'PIL._imagingft',
  'PIL._imagingmath',
  'PIL._imagingmorph',
  'PIL._imagingtk',
  'PIL._tkinter_finder',
  'PIL._util',
  'PIL._version',
  'PIL._webp',
  'PIL.features',
  'comtypes',
  'comtypes.GUID',
  'comtypes._comobject',
  'comtypes._memberspec',
  'comtypes._meta',
  'comtypes._npsupport',
  'comtypes._post_coinit',
  'comtypes._post_coinit._cominterface_meta_patcher',
  'comtypes._post_coinit.bstr',
  'comtypes._post_coinit.instancemethod',
  'comtypes._post_coinit.misc',
  'comtypes._post_coinit.unknwn',
  'comtypes._safearray',
  'comtypes._tlib_version_checker',
  'comtypes._vtbl',
  'comtypes.automation',
  'comtypes.clear_cache',
  'comtypes.client',
  'comtypes.client._activeobj',
  'comtypes.client._code_cache',
  'comtypes.client._constants',
  'comtypes.client._create',
  'comtypes.client._events',
  'comtypes.client._generate',
  'comtypes.client._managing',
  'comtypes.client.dynamic',
  'comtypes.client.lazybind',
  'comtypes.connectionpoints',
  'comtypes.errorinfo',
  'comtypes.gen',
  'comtypes.gen.UIAutomationClient',
  'comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0',
  'comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0',
  'comtypes.gen.stdole',
  'comtypes.git',
  'comtypes.hresult',
  'comtypes.logutil',
  'comtypes.messageloop',
  'comtypes.patcher',
  'comtypes.persist',
  'comtypes.safearray',
  'comtypes.server',
  'comtypes.server.automation',
  'comtypes.server.connectionpoints',
  'comtypes.server.inprocserver',
  'comtypes.server.localserver',
  'comtypes.server.register',
  'comtypes.server.w_getopt',
  'comtypes.shelllink',
  'comtypes.stream',
  'comtypes.test',
  'comtypes.test.TestComServer',
  'comtypes.test.TestDispServer',
  'comtypes.test.find_memleak',
  'comtypes.test.runtests',
  'comtypes.test.setup',
  'comtypes.test.test_BSTR',
  'comtypes.test.test_DISPPARAMS',
  'comtypes.test.test_GUID',
  'comtypes.test.test_QueryService',
  'comtypes.test.test_agilent',
  'comtypes.test.test_avmc',
  'comtypes.test.test_basic',
  'comtypes.test.test_casesensitivity',
  'comtypes.test.test_clear_cache',
  'comtypes.test.test_client',
  'comtypes.test.test_client_dynamic',
  'comtypes.test.test_client_regenerate_modules',
  'comtypes.test.test_collections',
  'comtypes.test.test_comobject',
  'comtypes.test.test_comserver',
  'comtypes.test.test_createwrappers',
  'comtypes.test.test_dict',
  'comtypes.test.test_dispifc_records',
  'comtypes.test.test_dispifc_safearrays',
  'comtypes.test.test_dispinterface',
  'comtypes.test.test_dyndispatch',
  'comtypes.test.test_errorinfo',
  'comtypes.test.test_excel',
  'comtypes.test.test_findgendir',
  'comtypes.test.test_getactiveobj',
  'comtypes.test.test_hresult',
  'comtypes.test.test_ie',
  'comtypes.test.test_ienum',
  'comtypes.test.test_imfattributes',
  'comtypes.test.test_inout_args',
  'comtypes.test.test_midl_safearray_create',
  'comtypes.test.test_monikers',
  'comtypes.test.test_msscript',
  'comtypes.test.test_npsupport',
  'comtypes.test.test_outparam',
  'comtypes.test.test_persist',
  'comtypes.test.test_pump_events',
  'comtypes.test.test_recordinfo',
  'comtypes.test.test_safearray',
  'comtypes.test.test_sapi',
  'comtypes.test.test_server',
  'comtypes.test.test_server_register',
  'comtypes.test.test_shelllink',
  'comtypes.test.test_showevents',
  'comtypes.test.test_storage',
  'comtypes.test.test_stream',
  'comtypes.test.test_subinterface',
  'comtypes.test.test_typeannotator',
  'comtypes.test.test_typeinfo',
  'comtypes.test.test_urlhistory',
  'comtypes.test.test_variant',
  'comtypes.test.test_w_getopt',
  'comtypes.test.test_win32com_interop',
  'comtypes.test.test_wmi',
  'comtypes.test.test_word',
  'comtypes.tools',
  'comtypes.tools.codegenerator',
  'comtypes.tools.codegenerator.codegenerator',
  'comtypes.tools.codegenerator.comments',
  'comtypes.tools.codegenerator.heads',
  'comtypes.tools.codegenerator.helpers',
  'comtypes.tools.codegenerator.modulenamer',
  'comtypes.tools.codegenerator.namespaces',
  'comtypes.tools.codegenerator.packing',
  'comtypes.tools.codegenerator.typeannotator',
  'comtypes.tools.tlbparser',
  'comtypes.tools.typedesc',
  'comtypes.tools.typedesc_base',
  'comtypes.typeinfo',
  'comtypes.util',
  'comtypes.viewobject',
  'PIL._imaging',
  'PIL._imagingtk',
  'PIL._tkinter_finder',
  'PIL.Image',
  'PIL.ImageChops',
  'PIL.ImageColor',
  'PIL.ImageCms',
  'PIL.ImageDraw',
  'PIL.ImageDraw2',
  'PIL.ImageEnhance',
  'PIL.ImageFile',
  'PIL.ImageFilter',
  'PIL.ImageFont',
  'PIL.ImageGrab',
  'PIL.ImageMath',
  'PIL.ImageMorph',
  'PIL.ImageOps',
  'PIL.ImagePalette',
  'PIL.ImagePath',
  'PIL.ImageQt',
  'PIL.ImageSequence',
  'PIL.ImageShow',
  'PIL.ImageStat',
  'PIL.ImageTk',
  'PIL.ImageTransform',
  'PIL.ImageWin',
  'PIL.ImageDraw2',
  'PIL.ImageDraw2.Draw',
  'PIL.ImageDraw2.Font'],
 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\numpy\\_pyinstaller',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\rapidfuzz\\__pyinstaller',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks\\__pycache__',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\__pycache__',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 ['tkinter',
  'matplotlib',
  'scipy',
  'pandas',
  'jupyter',
  'IPython',
  'notebook',
  'sphinx',
  'docutils',
  'pytest',
  'unittest',
  'doctest',
  'pdb',
  'bdb',
  'profile',
  'cProfile',
  'pstats',
  'timeit',
  'trace',
  'pickletools',
  'pydoc',
  'pydoc_data',
  'lib2to3',
  'setuptools',
  'distutils',
  'ensurepip',
  'venv',
  'virtualenv',
  'pip',
  'wheel',
  'easy_install',
  'cv2',
  'numpy',
  '__main__'],
 [],
 False,
 {},
 [],
 [('UIAutomationCore.dll', 'D:\\python\\微信客服\\UIAutomationCore.dll', 'DATA'),
  ('_imaging.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'DATA'),
  ('_imagingcms.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingcms.cp38-win_amd64.pyd',
   'DATA'),
  ('_imagingft.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingft.cp38-win_amd64.pyd',
   'DATA'),
  ('_imagingmath.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingmath.cp38-win_amd64.pyd',
   'DATA'),
  ('_imagingmorph.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingmorph.cp38-win_amd64.pyd',
   'DATA'),
  ('_imagingtk.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'DATA'),
  ('_webp.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'DATA'),
  ('config.ini', 'D:\\python\\微信客服\\config.ini', 'DATA')],
 '3.8.10 (tags/v3.8.10:3d8993a, May  3 2021, 11:48:03) [MSC v.1928 64 bit '
 '(AMD64)]',
 [('pyi_rth_win32comgenpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('send_message_clean',
   'D:\\python\\微信客服\\send_message_clean.py',
   'PYSOURCE')],
 [('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\struct.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ssl.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\csv.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\plistlib.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bz2.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('win32com',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('comtypes.viewobject',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\viewobject.py',
   'PYMODULE'),
  ('comtypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\util.py',
   'PYMODULE'),
  ('comtypes.typeinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\typeinfo.py',
   'PYMODULE'),
  ('comtypes.tools.typedesc_base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\typedesc_base.py',
   'PYMODULE'),
  ('comtypes.tools.typedesc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\typedesc.py',
   'PYMODULE'),
  ('comtypes.tools.tlbparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\tlbparser.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.typeannotator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\typeannotator.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.packing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\packing.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.namespaces',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\namespaces.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.modulenamer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\modulenamer.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\helpers.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.heads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\heads.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.comments',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\comments.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.codegenerator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\codegenerator.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\codegenerator\\__init__.py',
   'PYMODULE'),
  ('comtypes.tools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\tools\\__init__.py',
   'PYMODULE'),
  ('comtypes.test.test_word',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_word.py',
   'PYMODULE'),
  ('comtypes.test.test_wmi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_wmi.py',
   'PYMODULE'),
  ('comtypes.test.test_win32com_interop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_win32com_interop.py',
   'PYMODULE'),
  ('comtypes.test.test_w_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_w_getopt.py',
   'PYMODULE'),
  ('comtypes.test.test_variant',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_variant.py',
   'PYMODULE'),
  ('comtypes.test.test_urlhistory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_urlhistory.py',
   'PYMODULE'),
  ('comtypes.test.test_typeinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_typeinfo.py',
   'PYMODULE'),
  ('comtypes.test.test_typeannotator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_typeannotator.py',
   'PYMODULE'),
  ('comtypes.test.test_subinterface',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_subinterface.py',
   'PYMODULE'),
  ('test.test_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\test\\test_support.py',
   'PYMODULE'),
  ('sched',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\sched.py',
   'PYMODULE'),
  ('test.support.script_helper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\test\\support\\script_helper.py',
   'PYMODULE'),
  ('test.support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('test.support.testresult',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('nntplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nntplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\smtplib.py',
   'PYMODULE'),
  ('test',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\test\\__init__.py',
   'PYMODULE'),
  ('comtypes.test.test_stream',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_stream.py',
   'PYMODULE'),
  ('comtypes.test.test_storage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_storage.py',
   'PYMODULE'),
  ('comtypes.test.test_showevents',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_showevents.py',
   'PYMODULE'),
  ('comtypes.test.test_shelllink',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_shelllink.py',
   'PYMODULE'),
  ('comtypes.test.test_server_register',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_server_register.py',
   'PYMODULE'),
  ('comtypes.test.test_server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_server.py',
   'PYMODULE'),
  ('comtypes.test.test_sapi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_sapi.py',
   'PYMODULE'),
  ('comtypes.test.test_safearray',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_safearray.py',
   'PYMODULE'),
  ('comtypes.test.test_recordinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_recordinfo.py',
   'PYMODULE'),
  ('comtypes.test.test_pump_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_pump_events.py',
   'PYMODULE'),
  ('comtypes.test.test_persist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_persist.py',
   'PYMODULE'),
  ('comtypes.test.test_outparam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_outparam.py',
   'PYMODULE'),
  ('comtypes.test.test_npsupport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_npsupport.py',
   'PYMODULE'),
  ('comtypes.test.test_msscript',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_msscript.py',
   'PYMODULE'),
  ('comtypes.test.test_monikers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_monikers.py',
   'PYMODULE'),
  ('comtypes.test.test_midl_safearray_create',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_midl_safearray_create.py',
   'PYMODULE'),
  ('comtypes.test.test_inout_args',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_inout_args.py',
   'PYMODULE'),
  ('comtypes.test.test_imfattributes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_imfattributes.py',
   'PYMODULE'),
  ('comtypes.test.test_ienum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_ienum.py',
   'PYMODULE'),
  ('comtypes.test.test_ie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_ie.py',
   'PYMODULE'),
  ('comtypes.test.test_hresult',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_hresult.py',
   'PYMODULE'),
  ('comtypes.test.test_getactiveobj',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_getactiveobj.py',
   'PYMODULE'),
  ('comtypes.test.test_findgendir',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_findgendir.py',
   'PYMODULE'),
  ('comtypes.test.test_excel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_excel.py',
   'PYMODULE'),
  ('comtypes.test.test_errorinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_errorinfo.py',
   'PYMODULE'),
  ('comtypes.test.test_dyndispatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_dyndispatch.py',
   'PYMODULE'),
  ('comtypes.test.test_dispinterface',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_dispinterface.py',
   'PYMODULE'),
  ('comtypes.test.test_dispifc_safearrays',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_dispifc_safearrays.py',
   'PYMODULE'),
  ('comtypes.test.test_dispifc_records',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_dispifc_records.py',
   'PYMODULE'),
  ('comtypes.test.test_dict',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_dict.py',
   'PYMODULE'),
  ('comtypes.test.test_createwrappers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_createwrappers.py',
   'PYMODULE'),
  ('comtypes.test.test_comserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_comserver.py',
   'PYMODULE'),
  ('comtypes.test.test_comobject',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_comobject.py',
   'PYMODULE'),
  ('comtypes.test.test_collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_collections.py',
   'PYMODULE'),
  ('comtypes.test.test_client_regenerate_modules',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_client_regenerate_modules.py',
   'PYMODULE'),
  ('comtypes.test.test_client_dynamic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_client_dynamic.py',
   'PYMODULE'),
  ('comtypes.test.test_client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_client.py',
   'PYMODULE'),
  ('comtypes.test.test_clear_cache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_clear_cache.py',
   'PYMODULE'),
  ('comtypes.test.test_casesensitivity',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_casesensitivity.py',
   'PYMODULE'),
  ('comtypes.test.test_basic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_basic.py',
   'PYMODULE'),
  ('comtypes.test.test_avmc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_avmc.py',
   'PYMODULE'),
  ('comtypes.test.test_agilent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_agilent.py',
   'PYMODULE'),
  ('comtypes.test.test_QueryService',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_QueryService.py',
   'PYMODULE'),
  ('comtypes.test.test_GUID',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_GUID.py',
   'PYMODULE'),
  ('comtypes.test.test_DISPPARAMS',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_DISPPARAMS.py',
   'PYMODULE'),
  ('comtypes.test.test_BSTR',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\test_BSTR.py',
   'PYMODULE'),
  ('comtypes.test.setup',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\setup.py',
   'PYMODULE'),
  ('comtypes.test.runtests',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\runtests.py',
   'PYMODULE'),
  ('comtypes.test.find_memleak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\find_memleak.py',
   'PYMODULE'),
  ('comtypes.test.TestDispServer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\TestDispServer.py',
   'PYMODULE'),
  ('comtypes.test.TestComServer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\TestComServer.py',
   'PYMODULE'),
  ('comtypes.test',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\test\\__init__.py',
   'PYMODULE'),
  ('comtypes.shelllink',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\shelllink.py',
   'PYMODULE'),
  ('comtypes.server.w_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\w_getopt.py',
   'PYMODULE'),
  ('comtypes.server.register',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\register.py',
   'PYMODULE'),
  ('comtypes.server.localserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\localserver.py',
   'PYMODULE'),
  ('comtypes.server.inprocserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\inprocserver.py',
   'PYMODULE'),
  ('comtypes.server.connectionpoints',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\connectionpoints.py',
   'PYMODULE'),
  ('comtypes.server.automation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\automation.py',
   'PYMODULE'),
  ('comtypes.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\server\\__init__.py',
   'PYMODULE'),
  ('comtypes.safearray',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\safearray.py',
   'PYMODULE'),
  ('comtypes.persist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\persist.py',
   'PYMODULE'),
  ('comtypes.patcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\patcher.py',
   'PYMODULE'),
  ('comtypes.messageloop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\messageloop.py',
   'PYMODULE'),
  ('comtypes.logutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\logutil.py',
   'PYMODULE'),
  ('comtypes.hresult',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\hresult.py',
   'PYMODULE'),
  ('comtypes.git',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\git.py',
   'PYMODULE'),
  ('comtypes.gen.stdole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\gen\\stdole.py',
   'PYMODULE'),
  ('comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\gen\\_944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0.py',
   'PYMODULE'),
  ('comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\gen\\_00020430_0000_0000_C000_000000000046_0_2_0.py',
   'PYMODULE'),
  ('comtypes.gen.UIAutomationClient',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\gen\\UIAutomationClient.py',
   'PYMODULE'),
  ('comtypes.errorinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\errorinfo.py',
   'PYMODULE'),
  ('comtypes.connectionpoints',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\connectionpoints.py',
   'PYMODULE'),
  ('comtypes.client.lazybind',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\lazybind.py',
   'PYMODULE'),
  ('comtypes.client.dynamic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\dynamic.py',
   'PYMODULE'),
  ('comtypes.client._managing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_managing.py',
   'PYMODULE'),
  ('comtypes.client._generate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_generate.py',
   'PYMODULE'),
  ('comtypes.client._events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_events.py',
   'PYMODULE'),
  ('comtypes.client._create',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_create.py',
   'PYMODULE'),
  ('comtypes.client._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_constants.py',
   'PYMODULE'),
  ('comtypes.client._code_cache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_code_cache.py',
   'PYMODULE'),
  ('comtypes.client._activeobj',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\_activeobj.py',
   'PYMODULE'),
  ('comtypes.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\client\\__init__.py',
   'PYMODULE'),
  ('comtypes.clear_cache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\clear_cache.py',
   'PYMODULE'),
  ('comtypes.automation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\automation.py',
   'PYMODULE'),
  ('comtypes._vtbl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_vtbl.py',
   'PYMODULE'),
  ('comtypes._tlib_version_checker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_tlib_version_checker.py',
   'PYMODULE'),
  ('comtypes._safearray',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_safearray.py',
   'PYMODULE'),
  ('comtypes._post_coinit.unknwn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_post_coinit\\unknwn.py',
   'PYMODULE'),
  ('comtypes._post_coinit.misc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_post_coinit\\misc.py',
   'PYMODULE'),
  ('comtypes._post_coinit.instancemethod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_post_coinit\\instancemethod.py',
   'PYMODULE'),
  ('comtypes._post_coinit.bstr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_post_coinit\\bstr.py',
   'PYMODULE'),
  ('comtypes._post_coinit._cominterface_meta_patcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_post_coinit\\_cominterface_meta_patcher.py',
   'PYMODULE'),
  ('comtypes._post_coinit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_post_coinit\\__init__.py',
   'PYMODULE'),
  ('comtypes._npsupport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_npsupport.py',
   'PYMODULE'),
  ('comtypes._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_meta.py',
   'PYMODULE'),
  ('comtypes._memberspec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_memberspec.py',
   'PYMODULE'),
  ('comtypes._comobject',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\_comobject.py',
   'PYMODULE'),
  ('comtypes.GUID',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\GUID.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\__main__.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WalImageFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\WalImageFile.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\fractions.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TarIO',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\TarIO.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\imp.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcfFontFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PcfFontFile.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PSDraw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\PSDraw.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImageTransform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageTransform.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMorph',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageMorph.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GdImageFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\GdImageFile.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FontFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\FontFile.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.ContainerIO',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ContainerIO.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BdfFontFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\BdfFontFile.py',
   'PYMODULE'),
  ('wxauto.wxauto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\wxauto.py',
   'PYMODULE'),
  ('wxauto.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\utils.py',
   'PYMODULE'),
  ('wxauto.uiautomation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\uiautomation.py',
   'PYMODULE'),
  ('wxauto.languages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\languages.py',
   'PYMODULE'),
  ('wxauto.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\errors.py',
   'PYMODULE'),
  ('wxauto.elements',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\elements.py',
   'PYMODULE'),
  ('wxauto.color',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\color.py',
   'PYMODULE'),
  ('comtypes.gen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\gen\\__init__.py',
   'PYMODULE'),
  ('comtypes.stream',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\stream.py',
   'PYMODULE'),
  ('comtypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\comtypes\\__init__.py',
   'PYMODULE'),
  ('pyautogui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pygetwindow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pyrect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('mouseinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('pyscreeze',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pymsgbox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pytweening',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\glob.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('netbios',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('wxauto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wxauto\\__init__.py',
   'PYMODULE')],
 [('UIAutomationCore.dll', 'D:\\python\\微信客服\\UIAutomationCore.dll', 'BINARY'),
  ('_imaging.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'BINARY'),
  ('_imagingcms.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingcms.cp38-win_amd64.pyd',
   'BINARY'),
  ('_imagingft.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingft.cp38-win_amd64.pyd',
   'BINARY'),
  ('_imagingmath.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingmath.cp38-win_amd64.pyd',
   'BINARY'),
  ('_imagingmorph.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingmorph.cp38-win_amd64.pyd',
   'BINARY'),
  ('_imagingtk.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'BINARY'),
  ('_webp.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'BINARY'),
  ('python38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python38.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pywin32_system32\\pywintypes38.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pywin32_system32\\pythoncom38.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmorph.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingmorph.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingmath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingft.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingcms.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32clipboard.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32clipboard.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('config.ini', 'D:\\python\\微信客服\\config.ini', 'DATA'),
  ('base_library.zip',
   'D:\\python\\微信客服\\build\\send_message_clean\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\METADATA',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\RECORD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\INSTALLER',
   'DATA')])
