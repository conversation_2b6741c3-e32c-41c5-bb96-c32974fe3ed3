#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数据库测试脚本
"""

import pymysql
import configparser

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def set_test_data():
    """设置测试数据"""
    print("=== 设置测试数据 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 找到最近的几个订单并设置chatnote为0作为测试
        cursor.execute("""
            SELECT id, aid, ordersn, name, status
            FROM ims_dayu_workorder_order 
            WHERE paystatus = 1 
            AND (status = 0 OR status = 6)
            ORDER BY createtime DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 个可用于测试的订单:")
        
        for i, row in enumerate(results):
            print(f"{i+1}. 订单ID: {row[0]}, 机构: {row[1]}, 订单号: {row[2]}, 姓名: {row[3]}, 状态: {row[4]}")
        
        if results:
            # 设置前3个订单的chatnote为0
            test_orders = results[:3]
            order_ids = [row[0] for row in test_orders]
            
            for order_id in order_ids:
                cursor.execute("""
                    UPDATE ims_dayu_workorder_order 
                    SET chatnote = 0 
                    WHERE id = %s
                """, (order_id,))
            
            connection.commit()
            print(f"\n已将以下订单的chatnote设置为0:")
            for order in test_orders:
                print(f"  订单ID: {order[0]}, 订单号: {order[2]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 设置测试数据失败: {str(e)}")

def test_notification_query():
    """测试通知查询"""
    print("\n=== 测试通知查询 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT DISTINCT
                o.id,
                o.aid,
                o.ordersn as order_number,
                o.name as customer_name,
                o.price as total_amount,
                o.hospitalName,
                o.mealName,
                FROM_UNIXTIME(o.createtime) as create_time,
                o.pvalue as appointment_code,
                o.status,
                FROM_UNIXTIME(o.restime) as cancel_time
            FROM ims_dayu_workorder_order o
            WHERE o.chatnote = 0
            AND o.paystatus = 1
            AND (o.status = 0 OR o.status = 6)
            ORDER BY o.createtime DESC
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        print(f"查询到 {len(results)} 条待发送通知:")
        
        for row in results:
            print(f"\n订单ID: {row[0]}")
            print(f"机构ID: {row[1]}")
            print(f"订单号: {row[2]}")
            print(f"客户姓名: {row[3]}")
            print(f"医院: {row[5]}")
            print(f"套餐: {row[6]}")
            print(f"状态: {row[9]} ({'新预约' if row[9] == 0 else '取消预约' if row[9] == 6 else '其他'})")
            print(f"创建时间: {row[7]}")
            
            # 获取详细数据
            cursor.execute("""
                SELECT fid_name, data 
                FROM ims_dayu_workorder_data 
                WHERE oid = %s AND aid = %s AND fid_name IS NOT NULL
                ORDER BY displayorder
            """, (row[0], row[1]))
            
            detail_results = cursor.fetchall()
            if detail_results:
                print("详细信息:")
                for detail in detail_results:
                    if detail[0] and detail[1]:
                        print(f"  {detail[0]}: {detail[1]}")
            print("-" * 60)
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 测试通知查询失败: {str(e)}")

def main():
    """主函数"""
    print("简化的数据库测试")
    print("=" * 40)
    
    set_test_data()
    test_notification_query()
    
    print("\n测试完成")

if __name__ == "__main__":
    main()