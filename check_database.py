#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库状态检查脚本
"""

import pymysql
import configparser
from datetime import datetime, timedelta

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def check_chatnote_distribution():
    """检查chatnote字段分布情况"""
    print("\n=== chatnote字段分布检查 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 检查最近7天的订单chatnote分布
        print("最近7天的订单chatnote分布:")
        cursor.execute("""
            SELECT 
                chatnote,
                COUNT(*) as count,
                CONCAT(ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2), '%') as percentage
            FROM ims_dayu_workorder_order 
            WHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY))
            AND paystatus = 1
            GROUP BY chatnote
            ORDER BY chatnote
        """)
        
        results = cursor.fetchall()
        for row in results:
            chatnote_val = row[0] if row[0] is not None else 'NULL'
            print(f"  chatnote = {chatnote_val}: {row[1]} 条 ({row[2]})")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 检查chatnote分布失败: {str(e)}")

def check_recent_orders():
    """检查最近的订单情况"""
    print("\n=== 最近订单情况检查 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 检查最近1小时的订单
        print("最近1小时的订单:")
        cursor.execute("""
            SELECT 
                id, aid, ordersn, name, status, chatnote,
                FROM_UNIXTIME(createtime) as create_time
            FROM ims_dayu_workorder_order 
            WHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR))
            AND paystatus = 1
            ORDER BY createtime DESC
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 条最近1小时的订单:")
        for row in results:
            print(f"  ID: {row[0]}, 机构: {row[1]}, 订单号: {row[2]}")
            print(f"  姓名: {row[3]}, 状态: {row[4]}, chatnote: {row[5]}")
            print(f"  创建时间: {row[6]}")
            print("-" * 40)
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 检查最近订单失败: {str(e)}")

def check_data_fields():
    """检查数据表字段情况"""
    print("\n=== 数据表字段检查 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 获取一个最新的订单
        cursor.execute("""
            SELECT id, aid FROM ims_dayu_workorder_order 
            WHERE paystatus = 1 
            ORDER BY createtime DESC 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("没有找到订单")
            return
            
        order_id, aid = result
        print(f"检查订单ID: {order_id}, 机构ID: {aid}")
        
        # 检查该订单的数据字段
        cursor.execute("""
            SELECT fid, fid_name, data, displayorder
            FROM ims_dayu_workorder_data 
            WHERE oid = %s AND aid = %s
            ORDER BY displayorder
        """, (order_id, aid))
        
        data_results = cursor.fetchall()
        print(f"找到 {len(data_results)} 条数据记录:")
        for row in data_results:
            fid_name = row[1] if row[1] else f"fid_{row[0]}"
            print(f"  fid: {row[0]}, fid_name: '{row[1]}', data: '{row[2]}', order: {row[3]}")
        
        # 检查是否有常见的字段名
        cursor.execute("""
            SELECT DISTINCT fid_name, COUNT(*) as count
            FROM ims_dayu_workorder_data 
            WHERE fid_name IS NOT NULL AND fid_name != ''
            GROUP BY fid_name
            ORDER BY count DESC
            LIMIT 20
        """)
        
        print("\n常见的fid_name字段:")
        field_results = cursor.fetchall()
        for row in field_results:
            print(f"  '{row[0]}': {row[1]} 次")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 检查数据表字段失败: {str(e)}")

def set_test_chatnote():
    """设置一些测试订单的chatnote为0"""
    print("\n=== 设置测试数据 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 找到最近几个订单并设置chatnote为0
        cursor.execute("""
            SELECT id FROM ims_dayu_workorder_order 
            WHERE paystatus = 1 
            AND (status = 0 OR status = 6)
            ORDER BY createtime DESC 
            LIMIT 3
        """)
        
        results = cursor.fetchall()
        if results:
            order_ids = [str(row[0]) for row in results]
            placeholders = ','.join(['%s'] * len(order_ids))
            
            cursor.execute(f"""
                UPDATE ims_dayu_workorder_order 
                SET chatnote = 0 
                WHERE id IN ({placeholders})
            """, order_ids)
            
            connection.commit()
            print(f"已将订单 {', '.join(order_ids)} 的chatnote设置为0")
        else:
            print("没有找到符合条件的订单")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 设置测试数据失败: {str(e)}")

def main():
    """主函数"""
    print("开始数据库状态检查")
    print("=" * 60)
    
    check_chatnote_distribution()
    check_recent_orders()
    check_data_fields()
    
    # 询问是否设置测试数据
    print("\n是否要设置一些测试订单的chatnote为0? (y/n): ", end="")
    try:
        choice = input().strip().lower()
        if choice == 'y':
            set_test_chatnote()
    except:
        pass
    
    print("\n数据库状态检查完成")

if __name__ == "__main__":
    main()