
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named org - imported by copy (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), test.support (optional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), psutil (optional), getpass (delayed), netrc (delayed, conditional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), http.server (delayed, optional), webbrowser (delayed), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named ctypes._FuncPointer - imported by ctypes (conditional), comtypes._vtbl (conditional)
missing module named ctypes._CDataType - imported by ctypes (conditional), comtypes._memberspec (conditional), comtypes.automation (conditional)
missing module named ctypes._CArgObject - imported by ctypes (conditional), comtypes._memberspec (conditional), comtypes.automation (conditional), comtypes._comobject (conditional), comtypes.messageloop (conditional), comtypes.connectionpoints (conditional)
missing module named ctypes._CData - imported by ctypes (conditional), comtypes (conditional)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named jnius - imported by pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named platformdirs - imported by pkg_resources._vendor.platformdirs.__main__ (top-level)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _aix_support - imported by setuptools._distutils.py38compat (delayed, optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.register (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (conditional), setuptools.compat.py310 (conditional)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.msvc (top-level), setuptools.dist (top-level), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools._reqs (top-level), setuptools._core_metadata (top-level), setuptools.config.setupcfg (top-level), setuptools.command._requirestxt (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by setuptools.compat.py310 (conditional)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools._core_metadata (top-level), setuptools.depends (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools._core_metadata (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level)
missing module named 'setuptools.extern.ordered_set' - imported by setuptools.dist (top-level)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named anytree - imported by wxauto.utils (optional)
excluded module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named 'PyQt5.QtWidgets' - imported by pyperclip (delayed, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
excluded module named PIL - imported by wxauto.uiautomation (top-level), wxauto.utils (top-level)
missing module named comtypes.gen.Word - imported by comtypes.gen (optional), comtypes.test.test_word (optional)
missing module named comtypes.gen.WbemScripting - imported by comtypes.gen (delayed), comtypes.test.test_wmi (delayed)
missing module named cPickle - imported by comtypes.test.test_variant (delayed, conditional)
missing module named cStringIO - imported by cPickle (top-level)
missing module named copy_reg - imported by cPickle (top-level), cStringIO (top-level)
missing module named comtypes.gen.urlhistLib - imported by comtypes.gen (top-level), comtypes.test.test_urlhistory (top-level)
excluded module named tkinter - imported by test.support (delayed, conditional, optional)
missing module named 'comtypes.gen.PortableDeviceApiLib' - imported by comtypes.test.test_stream (top-level)
missing module named comtypes.gen.MSVidCtlLib - imported by comtypes.gen (delayed), comtypes.test.test_ienum (delayed), comtypes.test.test_imfattributes (delayed), comtypes.test.test_monikers (top-level), comtypes.test.test_storage (top-level)
missing module named comtypes.gen.TestLib - imported by comtypes.gen (top-level), comtypes.test.test_server (top-level)
missing module named comtypes.gen.SpeechLib - imported by comtypes.gen (delayed), comtypes.test.test_client (delayed), comtypes.test.test_sapi (delayed)
missing module named 'comtypes.gen.ComtypesCppTestSrvLib' - imported by comtypes.test.test_dispifc_records (optional), comtypes.test.test_dispifc_safearrays (optional), comtypes.test.test_midl_safearray_create (optional), comtypes.test.test_recordinfo (optional)
missing module named comtypes.gen.TestComServerLib - imported by comtypes.gen (top-level), comtypes.test.TestComServer (top-level), comtypes.test.test_comserver (delayed), comtypes.test.test_npsupport (delayed)
excluded module named numpy - imported by comtypes._npsupport (delayed, conditional), comtypes.test.test_npsupport (optional)
missing module named 'comtypes.gen.SHDocVw' - imported by comtypes.test.test_ie (delayed)
missing module named 'comtypes.gen.Excel' - imported by comtypes.test.test_excel (optional)
missing module named comtypes.gen.Scripting - imported by comtypes.gen (top-level), comtypes.test.test_client (top-level), comtypes.test.test_client_regenerate_modules (top-level), comtypes.test.test_comobject (top-level), comtypes.test.test_dyndispatch (top-level)
missing module named comtypes.gen.IviScopeLib - imported by comtypes.gen (delayed, optional), comtypes.test.test_agilent (delayed, optional)
missing module named 'comtypes.gen.Accessibility' - imported by comtypes.test.test_QueryService (top-level)
missing module named py2exe - imported by comtypes.test.setup (top-level)
missing module named comtypes.gen.TestDispServerLib - imported by comtypes.gen (top-level), comtypes.test.TestDispServer (top-level)
missing module named 'numpy.ctypeslib' - imported by comtypes._npsupport (delayed, optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named chardet - imported by requests (optional)
missing module named nacl - imported by pymysql._auth (delayed, optional)
