
This file lists modules Py<PERSON>nstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named org - imported by copy (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), test.support (optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _manylinux - imported by pkg_resources._vendor.packaging._manylinux (delayed, optional), packaging._manylinux (delayed, optional)
missing module named jnius - imported by pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named platformdirs - imported by pkg_resources._vendor.platformdirs.__main__ (top-level)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), psutil (optional), getpass (delayed), netrc (delayed, conditional), webbrowser (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named comtypes.gen.Word - imported by comtypes.gen (optional), comtypes.test.test_word (optional)
excluded module named unittest - imported by comtypes.test (top-level), comtypes.test.test_BSTR (top-level), comtypes.test.test_DISPPARAMS (top-level), comtypes.test.test_GUID (top-level), comtypes.test.test_QueryService (top-level), comtypes.test.test_agilent (top-level), comtypes.test.test_avmc (top-level), comtypes.test.test_basic (top-level), comtypes.test.test_casesensitivity (top-level), comtypes.test.test_clear_cache (top-level), comtypes.test.test_client (top-level), comtypes.test.test_client_dynamic (top-level), comtypes.test.test_client_regenerate_modules (top-level), comtypes.test.test_collections (top-level), comtypes.test.test_comobject (top-level), comtypes.test.test_comserver (top-level), comtypes.test.test_createwrappers (top-level), comtypes.test.test_dict (top-level), comtypes.test.test_dispifc_records (top-level), comtypes.test.test_dispifc_safearrays (top-level), comtypes.test.test_dispinterface (top-level), comtypes.test.test_dyndispatch (top-level), comtypes.test.test_errorinfo (top-level), comtypes.test.test_excel (top-level), comtypes.test.test_findgendir (top-level), comtypes.test.test_getactiveobj (top-level), comtypes.test.test_hresult (top-level), comtypes.test.test_ie (top-level), comtypes.test.test_ienum (top-level), comtypes.test.test_imfattributes (top-level), comtypes.test.test_inout_args (top-level), comtypes.test.test_midl_safearray_create (top-level), comtypes.test.test_monikers (top-level), comtypes.test.test_msscript (top-level), comtypes.test.test_npsupport (top-level), comtypes.test.test_outparam (top-level), comtypes.test.test_persist (top-level), comtypes.test.test_pump_events (top-level), comtypes.test.test_recordinfo (top-level), comtypes.test.test_safearray (top-level), comtypes.test.test_sapi (top-level), comtypes.test.test_server (top-level), comtypes.test.test_server_register (top-level), comtypes.test.test_shelllink (top-level), comtypes.test.test_showevents (top-level), comtypes.test.test_storage (top-level), comtypes.test.test_stream (top-level), comtypes.test.test_subinterface (top-level), test.test_support (top-level), test.support (top-level), test.support.testresult (top-level), comtypes.test.test_typeannotator (top-level), comtypes.test.test_typeinfo (top-level), comtypes.test.test_urlhistory (top-level), comtypes.test.test_variant (top-level), comtypes.test.test_w_getopt (top-level), comtypes.test.test_win32com_interop (top-level), comtypes.test.test_wmi (top-level), comtypes.test.test_word (top-level)
missing module named comtypes.gen.WbemScripting - imported by comtypes.gen (delayed), comtypes.test.test_wmi (delayed)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional), comtypes.test.test_variant (delayed, conditional)
missing module named cStringIO - imported by cPickle (top-level), cffi.ffiplatform (optional)
missing module named copy_reg - imported by cPickle (top-level), cStringIO (top-level)
missing module named comtypes.gen.urlhistLib - imported by comtypes.gen (top-level), comtypes.test.test_urlhistory (top-level)
excluded module named distutils - imported by cffi._shimmed_dist_utils (optional), test.support (delayed)
excluded module named doctest - imported by pyrect (top-level), comtypes.test.test_comserver (top-level), comtypes.test.test_showevents (top-level), test.support (delayed)
excluded module named tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional), test.support (delayed, conditional, optional)
missing module named 'comtypes.gen.PortableDeviceApiLib' - imported by comtypes.test.test_stream (top-level)
missing module named comtypes.gen.MSVidCtlLib - imported by comtypes.gen (delayed), comtypes.test.test_ienum (delayed), comtypes.test.test_imfattributes (delayed), comtypes.test.test_monikers (top-level), comtypes.test.test_storage (top-level)
missing module named comtypes.gen.TestLib - imported by comtypes.gen (top-level), comtypes.test.test_server (top-level)
missing module named comtypes.gen.SpeechLib - imported by comtypes.gen (delayed), comtypes.test.test_client (delayed), comtypes.test.test_sapi (delayed)
missing module named 'comtypes.gen.ComtypesCppTestSrvLib' - imported by comtypes.test.test_dispifc_records (optional), comtypes.test.test_dispifc_safearrays (optional), comtypes.test.test_midl_safearray_create (optional), comtypes.test.test_recordinfo (optional)
missing module named 'unittest.mock' - imported by comtypes.test.test_clear_cache (top-level), comtypes.test.test_inout_args (top-level), comtypes.test.test_outparam (top-level)
missing module named comtypes.gen.TestComServerLib - imported by comtypes.gen (top-level), comtypes.test.TestComServer (top-level), comtypes.test.test_comserver (delayed), comtypes.test.test_npsupport (delayed)
excluded module named numpy - imported by comtypes._npsupport (delayed, conditional), PIL.Image (delayed, conditional, optional), pyscreeze (optional), comtypes.test.test_npsupport (optional)
missing module named 'comtypes.gen.SHDocVw' - imported by comtypes.test.test_ie (delayed)
missing module named 'comtypes.gen.Excel' - imported by comtypes.test.test_excel (optional)
missing module named comtypes.gen.Scripting - imported by comtypes.gen (top-level), comtypes.test.test_client (top-level), comtypes.test.test_client_regenerate_modules (top-level), comtypes.test.test_comobject (top-level), comtypes.test.test_dyndispatch (top-level)
missing module named comtypes.gen.IviScopeLib - imported by comtypes.gen (delayed, optional), comtypes.test.test_agilent (delayed, optional)
missing module named 'comtypes.gen.Accessibility' - imported by comtypes.test.test_QueryService (top-level)
missing module named py2exe - imported by comtypes.test.setup (top-level)
missing module named 'distutils.core' - imported by cffi._shimmed_dist_utils (optional), comtypes.test.setup (top-level)
missing module named comtypes.gen.TestDispServerLib - imported by comtypes.gen (top-level), comtypes.test.TestDispServer (top-level)
missing module named ctypes._CArgObject - imported by ctypes (conditional), comtypes._memberspec (conditional), comtypes.automation (conditional), comtypes._comobject (conditional), comtypes.messageloop (conditional), comtypes.connectionpoints (conditional)
missing module named ctypes._CDataType - imported by ctypes (conditional), comtypes._memberspec (conditional), comtypes.automation (conditional)
missing module named ctypes._FuncPointer - imported by ctypes (conditional), comtypes._vtbl (conditional)
missing module named 'numpy.ctypeslib' - imported by comtypes._npsupport (delayed, optional)
missing module named 'distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named 'distutils.log' - imported by cffi._shimmed_dist_utils (optional)
missing module named 'distutils.errors' - imported by cffi._shimmed_dist_utils (optional)
missing module named 'distutils.dir_util' - imported by cffi._shimmed_dist_utils (optional)
missing module named 'distutils.command' - imported by cffi._shimmed_dist_utils (optional)
missing module named 'distutils.ccompiler' - imported by cffi._shimmed_dist_utils (optional)
excluded module named setuptools - imported by cffi._shimmed_dist_utils (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named anytree - imported by wxauto.utils (optional)
missing module named ctypes._CData - imported by ctypes (conditional), comtypes (conditional)
missing module named 'Xlib.XK' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.ext' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named Xlib - imported by mouseinfo (conditional), pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.display' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional), pyautogui._pyautogui_osx (top-level)
missing module named Quartz - imported by pygetwindow._pygetwindow_macos (top-level), pyautogui._pyautogui_osx (optional)
missing module named Tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional)
missing module named 'rubicon.objc' - imported by mouseinfo (conditional)
missing module named rubicon - imported by mouseinfo (conditional)
excluded module named cv2 - imported by pyscreeze (optional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named chardet - imported by requests (optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional)
missing module named _uuid - imported by uuid (optional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named nacl - imported by pymysql._auth (delayed, optional)
