#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动登录功能
"""

import win32gui
import time
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

def auto_click_login():
    """自动点击微信登录按钮"""
    try:
        # 查找微信窗口
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "微信" in window_text or "WeChat" in window_text:
                    windows.append((hwnd, window_text))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        logging.info(f"找到 {len(windows)} 个微信相关窗口")
        
        for hwnd, title in windows:
            # 检查是否是登录窗口（通常标题包含"登录"或窗口较小）
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            logging.info(f"窗口: {title} - 大小: {width}x{height}")
            
            # 登录窗口通常比主窗口小
            if width < 800 and height < 600:
                logging.info(f"检测到可能的登录窗口: {title} ({width}x{height})")
                
                # 尝试查找并点击登录按钮
                try:
                    # 激活窗口
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(1)
                    
                    # 尝试使用 wxauto
                    try:
                        import wxauto
                        wx = wxauto.WeChat()
                        if wx:
                            logging.info("微信对象创建成功，可能已经登录")
                            return True, "微信登录处理成功"
                    except Exception as e:
                        logging.warning(f"wxauto 创建失败: {str(e)}")
                    
                except Exception as e:
                    logging.warning(f"处理窗口失败: {str(e)}")
                    continue
        
        return False, "未找到登录窗口或自动登录失败"
        
    except Exception as e:
        logging.error(f"自动点击登录时出错: {str(e)}")
        return False, f"自动登录出错: {str(e)}"

def test_wechat_status():
    """测试微信状态"""
    try:
        import wxauto
        wx = wxauto.WeChat()
        if wx:
            logging.info("微信已登录，状态正常")
            return True
        else:
            logging.warning("微信未登录或无法连接")
            return False
    except Exception as e:
        logging.error(f"测试微信状态失败: {str(e)}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    logging.info("开始测试自动登录功能...")
    
    # 首先测试当前微信状态
    logging.info("步骤1: 测试当前微信状态")
    if test_wechat_status():
        logging.info("微信当前状态正常，无需自动登录")
        return
    
    # 尝试自动登录
    logging.info("步骤2: 尝试自动登录")
    success, msg = auto_click_login()
    
    if success:
        logging.info(f"自动登录成功: {msg}")
        
        # 再次测试状态
        time.sleep(3)
        if test_wechat_status():
            logging.info("自动登录后微信状态正常")
        else:
            logging.warning("自动登录后微信状态仍然异常")
    else:
        logging.error(f"自动登录失败: {msg}")

if __name__ == "__main__":
    main()
