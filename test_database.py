#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询测试脚本
"""

import pymysql
import configparser
from datetime import datetime

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def test_org_wechat_mapping():
    """测试机构微信映射查询"""
    print("\n=== 测试机构微信映射查询 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        cursor.execute("""
            SELECT aid, wechat_name, status, create_time
            FROM ims_dayu_workorder_org_wechat 
            WHERE status = 1
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 个启用的机构微信映射:")
        for row in results:
            print(f"  机构ID: {row[0]}, 微信名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 查询机构微信映射失败: {str(e)}")

def test_pending_notifications():
    """测试待发送通知查询"""
    print("\n=== 测试待发送通知查询 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        print("当前查询条件: chatnote = 2, paystatus = 1, status IN (0, 6)")
        cursor.execute("""
            SELECT DISTINCT
                o.id,
                o.aid,
                o.ordersn as order_number,
                o.name as customer_name,
                o.price as total_amount,
                o.hospitalName,
                o.mealName,
                FROM_UNIXTIME(o.createtime) as create_time,
                o.pvalue as appointment_code,
                o.status,
                FROM_UNIXTIME(o.restime) as cancel_time,
                o.chatnote
            FROM ims_dayu_workorder_order o
            WHERE o.chatnote = 2
            AND o.paystatus = 1
            AND (o.status = 0 OR o.status = 6)
            ORDER BY o.createtime DESC
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 条符合条件的订单:")
        for row in results:
            print(f"  订单ID: {row[0]}, 机构ID: {row[1]}, 订单号: {row[2]}")
            print(f"  客户姓名: {row[3]}, 状态: {row[9]}, chatnote: {row[11]}")
            print(f"  医院: {row[5]}, 套餐: {row[6]}")
            print(f"  创建时间: {row[7]}")
            print("-" * 50)
        
        # 测试查询 chatnote = 0 的订单
        print("\n--- 查询 chatnote = 0 的订单 ---")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM ims_dayu_workorder_order o
            WHERE o.chatnote = 0
            AND o.paystatus = 1
            AND (o.status = 0 OR o.status = 6)
        """)
        count = cursor.fetchone()[0]
        print(f"chatnote = 0 的订单数量: {count}")
        
        # 测试查询 chatnote IS NULL 的订单
        print("\n--- 查询 chatnote IS NULL 的订单 ---")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM ims_dayu_workorder_order o
            WHERE o.chatnote IS NULL
            AND o.paystatus = 1
            AND (o.status = 0 OR o.status = 6)
        """)
        count = cursor.fetchone()[0]
        print(f"chatnote IS NULL 的订单数量: {count}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 查询待发送通知失败: {str(e)}")

def test_order_details():
    """测试订单详细信息查询"""
    print("\n=== 测试订单详细信息查询 ===")
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 先获取一个订单ID
        cursor.execute("""
            SELECT id, aid FROM ims_dayu_workorder_order 
            WHERE paystatus = 1 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("没有找到订单")
            return
            
        order_id, aid = result
        print(f"测试订单ID: {order_id}, 机构ID: {aid}")
        
        cursor.execute("""
            SELECT fid_name, data 
            FROM ims_dayu_workorder_data 
            WHERE oid = %s AND aid = %s
            ORDER BY displayorder
        """, (order_id, aid))
        
        data_results = cursor.fetchall()
        print(f"找到 {len(data_results)} 条详细数据:")
        for row in data_results:
            print(f"  {row[0]}: {row[1]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 查询订单详细信息失败: {str(e)}")

def main():
    """主函数"""
    print("开始数据库查询测试")
    print("=" * 60)
    
    test_org_wechat_mapping()
    test_pending_notifications()
    test_order_details()
    
    print("\n数据库查询测试完成")

if __name__ == "__main__":
    main()