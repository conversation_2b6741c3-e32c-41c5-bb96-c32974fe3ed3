#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机构微信映射管理工具
用于管理 ims_dayu_workorder_org_wechat 表中的数据
"""

import pymysql
import configparser
import sys

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def show_current_mappings():
    """显示当前的机构微信映射"""
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        cursor.execute("""
            SELECT aid, wechat_name, status, create_time, update_time
            FROM ims_dayu_workorder_org_wechat 
            ORDER BY aid
        """)
        
        results = cursor.fetchall()
        if results:
            print(f"\n当前机构微信映射 ({len(results)} 条):")
            print("-" * 80)
            print(f"{'机构ID':<10} {'微信名称':<20} {'状态':<6} {'创建时间':<20} {'更新时间'}")
            print("-" * 80)
            for row in results:
                status_text = "启用" if row[2] == 1 else "禁用"
                print(f"{row[0]:<10} {row[1]:<20} {status_text:<6} {row[3]} {row[4]}")
        else:
            print("\n[INFO] 暂无机构微信映射数据")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 查询失败: {str(e)}")

def add_mapping():
    """添加机构微信映射"""
    try:
        print("\n添加机构微信映射")
        print("=" * 20)
        
        aid = input("请输入机构ID: ").strip()
        if not aid or not aid.isdigit():
            print("[ERROR] 机构ID必须是数字")
            return
        
        wechat_name = input("请输入微信名称: ").strip()
        if not wechat_name:
            print("[ERROR] 微信名称不能为空")
            return
        
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO ims_dayu_workorder_org_wechat (aid, wechat_name, status)
            VALUES (%s, %s, 1)
            ON DUPLICATE KEY UPDATE 
            wechat_name = VALUES(wechat_name),
            status = 1,
            update_time = NOW()
        """, (int(aid), wechat_name))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"[OK] 机构 {aid} -> 微信 '{wechat_name}' 映射添加成功")
        
    except Exception as e:
        print(f"[ERROR] 添加映射失败: {str(e)}")

def update_status():
    """更新映射状态"""
    try:
        show_current_mappings()
        
        print("\n更新映射状态")
        print("=" * 15)
        
        aid = input("请输入要更新的机构ID: ").strip()
        if not aid or not aid.isdigit():
            print("[ERROR] 机构ID必须是数字")
            return
        
        print("状态选项：")
        print("1. 启用")
        print("2. 禁用")
        choice = input("请选择 (1/2): ").strip()
        
        if choice == '1':
            status = 1
            status_text = "启用"
        elif choice == '2':
            status = 0
            status_text = "禁用"
        else:
            print("[ERROR] 无效选择")
            return
        
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        cursor.execute("""
            UPDATE ims_dayu_workorder_org_wechat 
            SET status = %s, update_time = NOW()
            WHERE aid = %s
        """, (status, int(aid)))
        
        if cursor.rowcount > 0:
            connection.commit()
            print(f"[OK] 机构 {aid} 状态已更新为: {status_text}")
        else:
            print(f"[ERROR] 未找到机构ID {aid}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 更新状态失败: {str(e)}")

def create_test_data():
    """创建测试数据"""
    try:
        print("\n创建测试数据")
        print("=" * 12)
        
        test_mappings = [
            (1001, "文件传输助手"),
            (1002, "测试联系人A"),
            (1003, "测试联系人B")
        ]
        
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        for aid, wechat_name in test_mappings:
            cursor.execute("""
                INSERT INTO ims_dayu_workorder_org_wechat (aid, wechat_name, status)
                VALUES (%s, %s, 1)
                ON DUPLICATE KEY UPDATE 
                wechat_name = VALUES(wechat_name),
                status = 1,
                update_time = NOW()
            """, (aid, wechat_name))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"[OK] 已创建 {len(test_mappings)} 条测试数据")
        show_current_mappings()
        
    except Exception as e:
        print(f"[ERROR] 创建测试数据失败: {str(e)}")

def main():
    """主菜单"""
    while True:
        print(f"\n机构微信映射管理工具")
        print("=" * 20)
        print("1. 查看当前映射")
        print("2. 添加映射")
        print("3. 更新状态")
        print("4. 创建测试数据")
        print("5. 退出")
        
        choice = input(f"\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            show_current_mappings()
        elif choice == '2':
            add_mapping()
        elif choice == '3':
            update_status()
        elif choice == '4':
            create_test_data()
        elif choice == '5':
            print("程序退出")
            break
        else:
            print("[ERROR] 无效选择")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")