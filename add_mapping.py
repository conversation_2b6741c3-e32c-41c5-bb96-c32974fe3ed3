#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加机构13的微信映射
"""

import pymysql
import configparser

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def add_mapping():
    """添加机构13的微信映射"""
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 添加机构13的微信映射
        cursor.execute("""
            INSERT INTO ims_dayu_workorder_org_wechat (aid, wechat_name, status)
            VALUES (13, '文件传输助手', 1)
            ON DUPLICATE KEY UPDATE 
            wechat_name = VALUES(wechat_name),
            status = 1,
            update_time = NOW()
        """)
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("[SUCCESS] 已添加机构13 -> 文件传输助手 的映射")
        
    except Exception as e:
        print(f"[ERROR] 添加映射失败: {str(e)}")

def main():
    """主函数"""
    print("添加机构13的微信映射")
    print("=" * 30)
    add_mapping()

if __name__ == "__main__":
    main()