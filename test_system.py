#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信发送测试脚本 - 不包含自动重启功能
"""

import pymysql
import configparser
from datetime import datetime

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def get_org_wechat_mapping():
    """获取机构微信映射关系"""
    try:
        connection = get_db_connection()
        if not connection:
            return {}
        
        cursor = connection.cursor()
        cursor.execute("""
            SELECT aid, wechat_name 
            FROM ims_dayu_workorder_org_wechat 
            WHERE status = 1
        """)
        
        mapping = {}
        for row in cursor.fetchall():
            mapping[row[0]] = row[1]
        
        cursor.close()
        connection.close()
        print(f"[SUCCESS] 获取到 {len(mapping)} 个机构微信映射")
        return mapping
    except Exception as e:
        print(f"[ERROR] 获取组织微信映射失败: {str(e)}")
        return {}

def get_pending_notifications():
    """获取待发送的通知"""
    try:
        connection = get_db_connection()
        if not connection:
            return []
        
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT DISTINCT
                o.id,
                o.aid,
                o.ordersn as order_number,
                o.name as customer_name,
                o.price as total_amount,
                o.hospitalName,
                o.mealName,
                FROM_UNIXTIME(o.createtime) as create_time,
                o.pvalue as appointment_code,
                o.status,
                FROM_UNIXTIME(o.restime) as cancel_time,
                a.title as activity_title
            FROM ims_dayu_workorder_order o
            LEFT JOIN ims_dayu_workorder_activity a ON o.aid = a.id
            WHERE o.chatnote = 2
            AND (o.status = 0 OR o.status = 6)
            ORDER BY o.createtime DESC
        """)
        
        notifications = []
        for row in cursor.fetchall():
            # 获取该订单的详细数据
            cursor.execute("""
                SELECT fid_name, data 
                FROM ims_dayu_workorder_data 
                WHERE oid = %s AND aid = %s AND fid_name IS NOT NULL
                ORDER BY displayorder
            """, (row[0], row[1]))
            
            order_data = cursor.fetchall()
            order_details = {}
            for data_row in order_data:
                if data_row[0] and data_row[1]:  # 两个字段都不为空
                    order_details[data_row[0]] = data_row[1]
            
            notification = {
                'id': row[0],
                'aid': row[1],
                'order_number': row[2],
                'customer_name': row[3] or order_details.get('姓名', '未知'),
                'gender': order_details.get('性别', ''),
                'id_card': order_details.get('身份证号码', ''),
                'phone': order_details.get('手机号', order_details.get('联系电话', '')),
                'product_name': row[6] or order_details.get('套餐名称', '体检套餐'),
                'appointment_time': order_details.get('预约时间', ''),
                'total_amount': float(row[4]) if row[4] else 0.00,
                'create_time': row[7],
                'cancel_time': row[10],
                'hospital_name': row[5] or row[11],  # 优先使用hospitalName，如果为空则使用activity.title
                'appointment_code': row[8],
                'order_status': row[9],
                'activity_title': row[11],  # 机构活动标题
                'order_details': order_details
            }
            notifications.append(notification)
        
        cursor.close()
        connection.close()
        print(f"[INFO] 获取到 {len(notifications)} 条待发送通知")
        return notifications
        
    except Exception as e:
        print(f"[ERROR] 获取待发送通知失败: {str(e)}")
        return []

def format_order_message(order):
    """格式化订单消息"""
    if order['order_status'] == 0:  # 新订单预约通知
        message = f"""[新订单预约通知]
预约人：{order['customer_name']}
预约性别：{order['gender']}
预约身份证：{order['id_card']}
预约套餐：{order['product_name']}
预约时间：{order['appointment_time']}
预约机构：{order['hospital_name']}

请及时安排接待~"""
    
    elif order['order_status'] == 6:  # 取消预约通知
        message = f"""[取消预约通知]
预约人：{order['customer_name']}
预约性别：{order['gender']}
预约身份证：{order['id_card']}
预约套餐：{order['product_name']}
预约时间：{order['appointment_time']}
预约机构：{order['hospital_name']}
取消时间：{order['cancel_time']}

该预约已被取消，请知悉~"""
    
    else:
        message = f"""[订单状态更新通知]
订单号：{order['order_number']}
客户姓名：{order['customer_name']}
状态：{order['order_status']}
机构：{order['hospital_name']}

请注意处理！"""
    
    return message

def update_notification_status(order_id, status):
    """更新通知状态"""
    try:
        connection = get_db_connection()
        if not connection:
            return False
        
        cursor = connection.cursor()
        chatnote_value = 1 if status == 'sent' else 0
        
        cursor.execute("""
            UPDATE ims_dayu_workorder_order 
            SET chatnote = %s 
            WHERE id = %s
        """, (chatnote_value, order_id))
        
        connection.commit()
        cursor.close()
        connection.close()
        return True
    except Exception as e:
        print(f"[ERROR] 更新通知状态失败: {str(e)}")
        return False

def test_system():
    """测试系统功能"""
    print("=== 微信客服系统功能测试 ===")
    
    # 1. 测试获取机构微信映射
    print("\n1. 测试机构微信映射...")
    org_wechat_mapping = get_org_wechat_mapping()
    if not org_wechat_mapping:
        print("[ERROR] 无法获取机构微信映射")
        return
    
    # 2. 测试获取待发送通知
    print("\n2. 测试获取待发送通知...")
    notifications = get_pending_notifications()
    if not notifications:
        print("[INFO] 暂无待发送通知")
        return
    
    # 3. 测试消息格式化和发送
    print(f"\n3. 处理 {len(notifications)} 条通知...")
    for notification in notifications:
        aid = notification['aid']
        if aid not in org_wechat_mapping:
            print(f"[WARNING] 机构 {aid} 未配置微信映射")
            continue
        
        wechat_name = org_wechat_mapping[aid]
        message = format_order_message(notification)
        
        print(f"\n--- 订单 {notification['id']} ---")
        print(f"发送目标: {wechat_name}")
        print(f"消息内容:\n{message}")
        
        # 模拟发送成功
        print("[INFO] 模拟发送消息...")
        
        # 更新状态为已发送
        if update_notification_status(notification['id'], 'sent'):
            print(f"[SUCCESS] 订单 {notification['id']} 状态已更新为已发送")
        else:
            print(f"[ERROR] 更新订单 {notification['id']} 状态失败")
        
        print("-" * 60)
    
    print("\n系统功能测试完成")

def main():
    """主函数"""
    test_system()

if __name__ == "__main__":
    main()