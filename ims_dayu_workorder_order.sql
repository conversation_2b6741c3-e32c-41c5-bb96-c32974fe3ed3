/*
Navicat MySQL Data Transfer

Source Server         : yihe.skyoupin.com_3306
Source Server Version : 50650
Source Host           : yihe.skyoupin.com:3306
Source Database       : we7

Target Server Type    : MYSQL
Target Server Version : 50650
File Encoding         : 65001

Date: 2025-07-31 15:37:22
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for ims_dayu_workorder_order
-- ----------------------------
DROP TABLE IF EXISTS `ims_dayu_workorder_order`;
CREATE TABLE `ims_dayu_workorder_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `weid` int(11) NOT NULL,
  `aid` int(11) NOT NULL,
  `uid` int(11) NOT NULL,
  `openid` varchar(50) NOT NULL,
  `ordersn` varchar(64) NOT NULL COMMENT '订单编号',
  `orderid` int(11) NOT NULL,
  `ordersn1` varchar(64) NOT NULL COMMENT '人人订单号',
  `transid` varchar(30) NOT NULL COMMENT '微信订单号',
  `paystatus` tinyint(1) NOT NULL COMMENT '付款状态',
  `paytype` tinyint(1) NOT NULL COMMENT '付款方式',
  `paydetail` varchar(100) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
  `remark` varchar(1000) NOT NULL,
  `state` tinyint(1) NOT NULL DEFAULT '0',
  `createtime` int(10) NOT NULL DEFAULT '0',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `top` tinyint(1) NOT NULL DEFAULT '0',
  `restime` int(10) NOT NULL DEFAULT '0',
  `fanid` int(11) NOT NULL,
  `information` varchar(1000) NOT NULL,
  `progress` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户进度',
  `card_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
  `staffid` int(11) NOT NULL,
  `uniontid` varchar(64) NOT NULL,
  `pvalue` varchar(20) DEFAULT NULL COMMENT '预约码',
  `name` varchar(50) DEFAULT NULL COMMENT '预约人姓名',
  `hospitalName` varchar(200) DEFAULT NULL,
  `mealName` varchar(200) DEFAULT NULL,
  `orderNum` varchar(50) DEFAULT NULL,
  `standard` text,
  `reportId` varchar(200) DEFAULT NULL,
  `apiorder` varchar(50) NOT NULL,
  `apistatus` tinyint(1) NOT NULL,
  `henum` int(5) NOT NULL,
  `baonum` int(5) NOT NULL,
  `cclientcode` varchar(20) NOT NULL,
  `fenzhang` tinyint(1) NOT NULL DEFAULT '0',
  `ispdf` tinyint(1) NOT NULL DEFAULT '0',
  `pdfurl` text NOT NULL,
  `order_platform` varchar(20) NOT NULL,
  `hxtime` datetime DEFAULT NULL,
  `typeid` int(10) NOT NULL,
  `issms` tinyint(1) NOT NULL,
  `issmstime` int(10) NOT NULL,
  `pvalue1` varchar(25) NOT NULL,
  `hxtime2` int(10) DEFAULT NULL,
  `chatnote` int(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `weid` (`weid`),
  KEY `aid` (`aid`),
  KEY `pvalue` (`pvalue`) USING BTREE,
  KEY `apiorder` (`apiorder`) USING BTREE,
  KEY `apistatus` (`apistatus`) USING BTREE,
  KEY `restime` (`restime`),
  KEY `createtime` (`createtime`),
  KEY `paystatus` (`paystatus`)
) ENGINE=InnoDB AUTO_INCREMENT=303414 DEFAULT CHARSET=utf8;
DROP TRIGGER IF EXISTS `set_hxtime_on_update`;
DELIMITER ;;
CREATE TRIGGER `set_hxtime_on_update` BEFORE UPDATE ON `ims_dayu_workorder_order` FOR EACH ROW BEGIN
    -- 只有状态有变动时才执行后续逻辑
    IF NEW.status <> OLD.status THEN

        -- 状态变更为 9：更新时间戳
        IF NEW.status = 9 THEN
            SET NEW.hxtime = NOW();
        ELSE
            SET NEW.hxtime = NULL;
        END IF;

        -- 状态变更为 6 或 9：清空 chatnote
        IF NEW.status IN (6, 9) THEN
            SET NEW.chatnote = NULL;
        END IF;

    END IF;
END
;;
DELIMITER ;
