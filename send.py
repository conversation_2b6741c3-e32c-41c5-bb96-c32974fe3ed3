#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信客服系统 - 精简版本
只包含实际使用的库，避免不必要的依赖
"""

import wxauto
import time
import configparser
import pymysql
from datetime import datetime
import os
import logging
import sys
import subprocess
import ctypes
import shutil
import win32gui
from PIL import ImageGrab, Image, ImageDraw, ImageFont
import psutil

def log_and_print(message, level="INFO"):
    """记录日志并打印到控制台"""
    if level == "ERROR":
        logging.error(message)
        print(f"[ERROR] {message}")
    elif level == "WARNING":
        logging.warning(message)
        print(f"[WARNING] {message}")
    elif level == "SUCCESS":
        logging.info(message)
        print(f"[SUCCESS] {message}")
    else:
        logging.info(message)
        print(f"[INFO] {message}")

def ensure_uiautomation_dll():
    """确保 UIAutomationCore.dll 已加载"""
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        dll_path = os.path.join(current_dir, "UIAutomationCore.dll")
        
        if not os.path.exists(dll_path):
            system_dlls = [
                os.path.join(os.environ['SystemRoot'], 'System32', 'UIAutomationCore.dll'),
                os.path.join(os.environ['SystemRoot'], 'SysWOW64', 'UIAutomationCore.dll')
            ]
            
            for system_dll in system_dlls:
                if os.path.exists(system_dll):
                    try:
                        shutil.copy2(system_dll, dll_path)
                        logging.info(f"已复制 UIAutomationCore.dll 从 {system_dll} 到 {dll_path}")
                        break
                    except Exception as copy_err:
                        logging.error(f"复制 UIAutomationCore.dll 失败: {str(copy_err)}")
        
        if os.path.exists(dll_path):
            try:
                ctypes.WinDLL(dll_path)
                logging.info("成功加载 UIAutomationCore.dll")
                return True
            except Exception as load_err:
                logging.error(f"加载 UIAutomationCore.dll 失败: {str(load_err)}")
        
        try:
            ctypes.WinDLL('UIAutomationCore.dll')
            logging.info("成功从系统路径加载 UIAutomationCore.dll")
            return True
        except Exception as sys_err:
            logging.error(f"从系统路径加载 UIAutomationCore.dll 失败: {str(sys_err)}")
            
        logging.error("无法加载 UIAutomationCore.dll")
        print("\n" + "*" * 50)
        print("错误: 无法加载 UIAutomationCore.dll")
        print("您可能需要安装 Windows 更新 KB971513")
        print("请访问: https://github.com/yinkaisheng/WindowsUpdateKB971513ForUIAutomation")
        print("*" * 50 + "\n")
        return False
    except Exception as e:
        logging.error(f"确保 UIAutomationCore.dll 过程中出错: {str(e)}")
        return False

def setup_logging():
    """设置日志"""
    log_dir = "log"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"{datetime.now().strftime('%Y-%m-%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def ensure_wechat_running():
    """确保微信正在运行"""
    try:
        wechat_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'WeChat' in proc.info['name']:
                    wechat_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if wechat_processes:
            log_and_print(f"检测到 {len(wechat_processes)} 个微信进程正在运行")
            return True
        else:
            log_and_print("未检测到微信进程，尝试启动微信...", "WARNING")
            
            wechat_paths = [
                os.path.expanduser("~\\AppData\\Local\\Tencent\\WeChat\\WeChat.exe"),
                "C:\\Program Files (x86)\\Tencent\\WeChat\\WeChat.exe",
                "C:\\Program Files\\Tencent\\WeChat\\WeChat.exe"
            ]
            
            for path in wechat_paths:
                if os.path.exists(path):
                    try:
                        subprocess.Popen([path])
                        log_and_print(f"已启动微信: {path}")
                        time.sleep(5)
                        return True
                    except Exception as e:
                        log_and_print(f"启动微信失败: {str(e)}", "ERROR")
                        continue
            
            log_and_print("无法找到微信程序，请手动启动微信", "ERROR")
            return False
            
    except Exception as e:
        log_and_print(f"检查微信状态时出错: {str(e)}", "ERROR")
        return False

def check_wechat_running():
    """检查微信是否正在运行"""
    try:
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'WeChat' in proc.info['name']:
                    return True, f"微信进程 ID: {proc.info['pid']}"
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False, "未检测到微信进程"
    except Exception as e:
        return False, f"检查微信状态时出错: {str(e)}"

def check_wechat_login_status():
    """检查微信登录状态"""
    try:
        wx = wxauto.WeChat()
        # 尝试获取微信对象，如果成功则说明已登录
        if wx:
            return True, "微信已登录"
        else:
            return False, "微信未登录或未找到微信窗口"
    except Exception as e:
        return False, f"检查微信登录状态时出错: {str(e)}"

def clear_comtypes_cache():
    """清理 comtypes 缓存"""
    try:
        import comtypes
        import shutil

        # 获取 comtypes 缓存目录
        cache_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Temp", "comtypes_cache")
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            log_and_print("已清理 comtypes 缓存目录", "INFO")

        # 清理 comtypes.gen 模块
        gen_dir = os.path.join(os.path.dirname(comtypes.__file__), "gen")
        if os.path.exists(gen_dir):
            for file in os.listdir(gen_dir):
                if file.endswith('.py') and file != '__init__.py':
                    try:
                        os.remove(os.path.join(gen_dir, file))
                    except:
                        pass
            log_and_print("已清理 comtypes.gen 模块", "INFO")

        return True
    except Exception as e:
        log_and_print(f"清理 comtypes 缓存失败: {str(e)}", "WARNING")
        return False

def detect_wechat_logout():
    """检测微信是否掉线 - 简化版本，避免 comtypes 错误"""
    try:
        # 首先检查微信进程是否存在
        wechat_running = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'WeChat' in proc.info['name']:
                    wechat_running = True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if not wechat_running:
            return True, "微信进程未运行"

        # 尝试创建微信对象，但不进行复杂操作
        try:
            wx = wxauto.WeChat()
            if wx:
                return False, "微信状态正常"
            else:
                return True, "无法连接到微信窗口"
        except ImportError as ie:
            if "comtypes" in str(ie):
                log_and_print("检测到 comtypes 导入错误，尝试清理缓存", "WARNING")
                clear_comtypes_cache()
                return True, "comtypes 模块错误，已清理缓存"
            else:
                return True, f"导入错误: {str(ie)}"
        except Exception as e:
            error_msg = str(e)
            if "comtypes" in error_msg:
                log_and_print("检测到 comtypes 相关错误，尝试清理缓存", "WARNING")
                clear_comtypes_cache()
                return True, "comtypes 相关错误，已清理缓存"
            else:
                return True, f"检测微信状态时出错: {error_msg}"

    except Exception as e:
        return True, f"检测微信状态时出错: {str(e)}"

def restart_wechat():
    """重启微信"""
    try:
        log_and_print("正在重启微信...", "WARNING")
        
        # 强制关闭微信进程
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'WeChat' in proc.info['name']:
                    proc.terminate()
                    log_and_print(f"已终止微信进程: {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        time.sleep(3)
        
        # 重新启动微信
        wechat_paths = [
            os.path.expanduser("~\\AppData\\Local\\Tencent\\WeChat\\WeChat.exe"),
            "C:\\Program Files (x86)\\Tencent\\WeChat\\WeChat.exe",
            "C:\\Program Files\\Tencent\\WeChat\\WeChat.exe"
        ]
        
        for path in wechat_paths:
            if os.path.exists(path):
                try:
                    subprocess.Popen([path])
                    log_and_print(f"已重新启动微信: {path}")
                    time.sleep(10)  # 等待微信启动
                    return True
                except Exception as e:
                    log_and_print(f"重启微信失败: {str(e)}", "ERROR")
                    continue
        
        return False
    except Exception as e:
        log_and_print(f"重启微信时出错: {str(e)}", "ERROR")
        return False

def capture_qr_code():
    """截取微信登录二维码"""
    try:
        # 查找微信窗口 - 使用win32gui直接查找
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "微信" in window_text or "WeChat" in window_text:
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if not windows:
            log_and_print("未找到微信窗口", "ERROR")
            return None
        
        # 使用第一个找到的微信窗口
        window = windows[0]
        
        # 获取窗口位置和大小
        rect = win32gui.GetWindowRect(window)
        x, y, width, height = rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1]
        
        # 截取整个微信窗口
        screenshot = ImageGrab.grab(bbox=(x, y, x + width, y + height))
        
        # 保存截图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"wechat_screenshot_{timestamp}.png"
        screenshot.save(screenshot_path)
        
        log_and_print(f"已截取微信窗口截图: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        log_and_print(f"截取二维码失败: {str(e)}", "ERROR")
        return None

def forward_qr_code(qr_path, method="fixed_path"):
    """转发二维码"""
    try:
        if not qr_path or not os.path.exists(qr_path):
            log_and_print("二维码文件不存在", "ERROR")
            return False
        
        if method == "fixed_path":
            # 保存到固定路径
            target_path = "D:\\wwwroot\\baogao.skyoupin.com\\code.jpg"
            target_dir = os.path.dirname(target_path)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
            shutil.copy2(qr_path, target_path)
            log_and_print(f"二维码已保存到固定路径: {target_path}", "SUCCESS")
            
        elif method == "save":
            # 保存到 qr_codes 目录
            qr_dir = "qr_codes"
            if not os.path.exists(qr_dir):
                os.makedirs(qr_dir)
            target_path = os.path.join(qr_dir, f"qr_code_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg")
            shutil.copy2(qr_path, target_path)
            log_and_print(f"二维码已保存到目录: {target_path}", "SUCCESS")
            
        elif method == "desktop":
            # 复制到桌面
            desktop = os.path.expanduser("~/Desktop")
            target_path = os.path.join(desktop, f"wechat_qr_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg")
            shutil.copy2(qr_path, target_path)
            log_and_print(f"二维码已复制到桌面: {target_path}", "SUCCESS")
        
        return True
    except Exception as e:
        log_and_print(f"转发二维码失败: {str(e)}", "ERROR")
        return False

def handle_wechat_relogin():
    """处理微信重新登录"""
    try:
        log_and_print("检测到微信掉线，开始处理重新登录...", "WARNING")
        
        # 重启微信
        if not restart_wechat():
            log_and_print("重启微信失败", "ERROR")
            return False
        
        # 等待登录界面出现
        time.sleep(5)
        
        # 截取二维码
        qr_path = capture_qr_code()
        if not qr_path:
            log_and_print("截取二维码失败", "ERROR")
            return False
        
        # 转发二维码
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        qr_method = config.get('wechat', 'qr_forward_method', fallback='fixed_path')
        
        if not forward_qr_code(qr_path, qr_method):
            log_and_print("转发二维码失败", "ERROR")
            return False
        
        log_and_print("请扫描二维码重新登录微信", "WARNING")
        return True
    except Exception as e:
        log_and_print(f"处理微信重新登录时出错: {str(e)}", "ERROR")
        return False

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        log_and_print(f"数据库连接失败: {str(e)}", "ERROR")
        return None

def get_org_wechat_mapping():
    """获取机构微信映射关系 - 使用现有的ims_dayu_workorder_org_wechat表"""
    try:
        connection = get_db_connection()
        if not connection:
            return {}
        
        cursor = connection.cursor()
        cursor.execute("""
            SELECT aid, wechat_name 
            FROM ims_dayu_workorder_org_wechat 
            WHERE status = 1
        """)
        
        mapping = {}
        for row in cursor.fetchall():
            mapping[row[0]] = row[1]
        
        cursor.close()
        connection.close()
        log_and_print(f"获取到 {len(mapping)} 个机构微信映射", "SUCCESS")
        return mapping
    except Exception as e:
        log_and_print(f"获取组织微信映射失败: {str(e)}", "ERROR")
        return {}

def get_pending_notifications():
    """获取待发送的通知 - 从订单表和数据表获取"""
    try:
        connection = get_db_connection()
        if not connection:
            return []
        
        cursor = connection.cursor()
        
        # 查询需要发送通知的订单 (chatnote=2表示需要发送)
        cursor.execute("""
            SELECT DISTINCT
                o.id,
                o.aid,
                o.ordersn as order_number,
                o.name as customer_name,
                o.price as total_amount,
                o.hospitalName,
                o.mealName,
                FROM_UNIXTIME(o.createtime) as create_time,
                o.pvalue as appointment_code,
                o.status,
                FROM_UNIXTIME(o.restime) as cancel_time,
                a.title as activity_title
            FROM ims_dayu_workorder_order o
            LEFT JOIN ims_dayu_workorder_activity a ON o.aid = a.id
            WHERE o.chatnote = 2
            AND (o.status = 0 OR o.status = 6)
            ORDER BY o.createtime DESC
        """)
        
        notifications = []
        for row in cursor.fetchall():
            # 获取该订单的详细数据
            cursor.execute("""
                SELECT fid_name, data 
                FROM ims_dayu_workorder_data 
                WHERE oid = %s AND aid = %s
                ORDER BY displayorder
            """, (row[0], row[1]))
            
            order_data = cursor.fetchall()
            order_details = {}
            for data_row in order_data:
                if data_row[0]:  # fid_name不为空
                    order_details[data_row[0]] = data_row[1]
            
            notification = {
                'id': row[0],
                'aid': row[1],
                'order_number': row[2],
                'customer_name': row[3] or order_details.get('姓名', '未知'),
                'gender': order_details.get('性别', ''),
                'id_card': order_details.get('身份证号码', ''),
                'phone': order_details.get('手机号', order_details.get('联系电话', '')),
                'product_name': row[6] or order_details.get('套餐名称', '体检套餐'),
                'appointment_time': order_details.get('预约时间', ''),
                'total_amount': float(row[4]) if row[4] else 0.00,
                'create_time': row[7],
                'cancel_time': row[10],
                'hospital_name': row[5] or row[11],  # 优先使用hospitalName，如果为空则使用activity.title
                'appointment_code': row[8],
                'order_status': row[9],  # 0=已预约, 6=取消预约
                'activity_title': row[11],  # 机构活动标题
                'order_details': order_details
            }
            notifications.append(notification)
        
        cursor.close()
        connection.close()
        log_and_print(f"获取到 {len(notifications)} 条待发送通知", "INFO")
        return notifications
        
    except Exception as e:
        log_and_print(f"获取待发送通知失败: {str(e)}", "ERROR")
        return []

def update_notification_status(order_id, status):
    """更新通知状态 - 使用订单表的chatnote字段标记"""
    try:
        connection = get_db_connection()
        if not connection:
            return False
        
        cursor = connection.cursor()
        # 发送成功后设置chatnote=1
        chatnote_value = 1 if status == 'sent' else 0  # 保持0表示未发送，等待重试
        
        cursor.execute("""
            UPDATE ims_dayu_workorder_order 
            SET chatnote = %s 
            WHERE id = %s
        """, (chatnote_value, order_id))
        
        connection.commit()
        cursor.close()
        connection.close()
        return True
    except Exception as e:
        log_and_print(f"更新通知状态失败: {str(e)}", "ERROR")
        return False

def send_message_to_wechat(wechat_name, message):
    """发送消息到微信 - 增强版本，包含错误处理"""
    try:
        # 首先检查微信进程是否存在
        wechat_running = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'WeChat' in proc.info['name']:
                    wechat_running = True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if not wechat_running:
            log_and_print("微信进程未运行，无法发送消息", "ERROR")
            return False

        # 创建微信对象
        wx = wxauto.WeChat()
        if not wx:
            log_and_print("无法连接到微信窗口", "ERROR")
            return False

        # 查找联系人
        wx.ChatWith(wechat_name)
        time.sleep(2)  # 增加等待时间

        # 发送消息
        wx.SendMsg(message)
        time.sleep(2)  # 增加等待时间

        log_and_print(f"消息已发送给 {wechat_name}", "SUCCESS")
        return True

    except ImportError as ie:
        if "comtypes" in str(ie):
            log_and_print(f"comtypes 导入错误，清理缓存: {str(ie)}", "WARNING")
            clear_comtypes_cache()
            return False
        else:
            log_and_print(f"导入错误: {str(ie)}", "ERROR")
            return False
    except Exception as e:
        error_msg = str(e)
        if "comtypes" in error_msg:
            log_and_print(f"comtypes 相关错误，清理缓存: {error_msg}", "WARNING")
            clear_comtypes_cache()
            return False
        else:
            log_and_print(f"发送消息失败: {error_msg}", "ERROR")
            return False

def send_message_to_wechat_with_relogin(wechat_name, message):
    """带重新登录功能的微信消息发送"""
    try:
        # 检查微信状态
        is_running, running_msg = check_wechat_running()
        if not is_running:
            log_and_print(running_msg, "WARNING")
            if not ensure_wechat_running():
                return False
        
        # 检查登录状态
        is_logged_in, login_msg = check_wechat_login_status()
        if not is_logged_in:
            log_and_print(login_msg, "WARNING")
            if not handle_wechat_relogin():
                return False
        
        # 发送消息
        return send_message_to_wechat(wechat_name, message)
    except Exception as e:
        log_and_print(f"发送消息时出错: {str(e)}", "ERROR")
        return False

def format_order_message(order):
    """格式化订单消息"""
    if order['order_status'] == 0:  # 新订单预约通知
        message = f"""[新订单预约通知]
预约人：{order['customer_name']}
预约性别：{order['gender']}
预约身份证：{order['id_card']}
预约套餐：{order['product_name']}
预约时间：{order['appointment_time']}
预约机构：{order['hospital_name']}

请及时安排接待~"""
    
    elif order['order_status'] == 6:  # 取消预约通知
        message = f"""[取消预约通知]
预约人：{order['customer_name']}
预约性别：{order['gender']}
预约身份证：{order['id_card']}
预约套餐：{order['product_name']}
预约时间：{order['appointment_time']}
预约机构：{order['hospital_name']}
取消时间：{order['cancel_time']}

该预约已被取消，请知悉~"""
    
    else:
        # 默认消息格式
        message = f"""[订单状态更新通知]
订单号：{order['order_number']}
客户姓名：{order['customer_name']}
状态：{order['order_status']}
机构：{order['hospital_name']}

请注意处理！"""
    
    return message

def is_work_time():
    """检查是否在工作时间内"""
    now = datetime.now()
    # 工作时间为 9:00-18:00
    if now.hour >= 9 and now.hour < 18:
        return True
    return False

def main_loop():
    """主循环"""
    log_and_print("微信客服系统启动（精简版本）", "SUCCESS")

    # 清理 comtypes 缓存，避免启动时的错误
    clear_comtypes_cache()

    # 确保 UIAutomationCore.dll 已加载
    if not ensure_uiautomation_dll():
        log_and_print("UIAutomationCore.dll 加载失败，程序可能无法正常工作", "WARNING")

    # 确保微信正在运行
    if not ensure_wechat_running():
        log_and_print("微信启动失败，请手动启动微信", "ERROR")
        return

    # 添加循环计数器，减少微信状态检测频率
    loop_count = 0
    last_wechat_check = 0
    wechat_check_interval = 300  # 5分钟检查一次微信状态

    while True:
        try:
            loop_count += 1
            current_time = time.time()

            # 检查是否在工作时间内
            if not is_work_time():
                log_and_print("当前不在工作时间内，等待中...", "INFO")
                time.sleep(300)  # 等待5分钟
                continue

            # 减少微信状态检测频率，只在必要时检查
            if current_time - last_wechat_check > wechat_check_interval:
                last_wechat_check = current_time
                is_logged_out, logout_msg = detect_wechat_logout()
                if is_logged_out:
                    log_and_print(logout_msg, "WARNING")
                    # 如果是 comtypes 错误，不要重启微信，只是等待
                    if "comtypes" in logout_msg:
                        log_and_print("comtypes 错误，跳过重启微信，等待下次检查", "INFO")
                        time.sleep(60)
                        continue
                    # 其他错误才考虑重启微信
                    if not handle_wechat_relogin():
                        log_and_print("重新登录失败，等待下次检查", "ERROR")
                        time.sleep(60)
                        continue
            
            # 获取组织微信映射
            org_wechat_mapping = get_org_wechat_mapping()
            if not org_wechat_mapping:
                log_and_print("未找到组织微信映射关系", "WARNING")
                time.sleep(60)
                continue
            
            # 获取待发送通知
            notifications = get_pending_notifications()
            if not notifications:
                log_and_print("暂无待发送通知", "INFO")
                time.sleep(60)
                continue
            
            log_and_print(f"发现 {len(notifications)} 条待发送通知", "INFO")
            
            # 处理每条通知
            for notification in notifications:
                try:
                    aid = notification['aid']
                    if aid not in org_wechat_mapping:
                        log_and_print(f"机构 {aid} 未配置微信映射", "WARNING")
                        # 跳过未配置的机构，但不标记为失败，等待配置后重试
                        continue
                    
                    wechat_name = org_wechat_mapping[aid]
                    message = format_order_message(notification)
                    
                    log_and_print(f"准备发送通知 {notification['id']} 到 {wechat_name}", "INFO")
                    
                    # 发送消息 - 使用简化的发送方法，避免过度检测
                    if send_message_to_wechat(wechat_name, message):
                        update_notification_status(notification['id'], 'sent')
                        log_and_print(f"通知 {notification['id']} 发送成功", "SUCCESS")
                    else:
                        # 发送失败，保持chatnote=0等待重试
                        log_and_print(f"通知 {notification['id']} 发送失败，将在下次循环重试", "ERROR")
                    
                    time.sleep(2)  # 避免发送过快
                    
                except Exception as e:
                    log_and_print(f"处理通知 {notification['id']} 时出错: {str(e)}", "ERROR")
                    # 处理异常时也不标记为失败，等待重试
            
            time.sleep(30)  # 等待30秒后继续检查
            
        except KeyboardInterrupt:
            log_and_print("程序被用户中断", "INFO")
            break
        except Exception as e:
            log_and_print(f"主循环出错: {str(e)}", "ERROR")
            time.sleep(60)

def main():
    """主函数"""
    setup_logging()
    
    try:
        main_loop()
    except Exception as e:
        log_and_print(f"程序运行出错: {str(e)}", "ERROR")
    finally:
        log_and_print("程序退出", "INFO")

if __name__ == "__main__":
    main() 