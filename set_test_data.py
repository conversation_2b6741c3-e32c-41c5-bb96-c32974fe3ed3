#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置chatnote=2的测试数据
"""

import pymysql
import configparser

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        return None

def set_test_data():
    """设置测试数据chatnote=2"""
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 找到最近的几个订单并设置chatnote为2作为测试
        cursor.execute("""
            SELECT o.id, o.aid, o.ordersn, o.name, o.status, a.title
            FROM ims_dayu_workorder_order o
            LEFT JOIN ims_dayu_workorder_activity a ON o.aid = a.id
            WHERE (o.status = 0 OR o.status = 6)
            ORDER BY o.createtime DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 个可用于测试的订单:")
        
        for i, row in enumerate(results):
            print(f"{i+1}. 订单ID: {row[0]}, 机构: {row[1]}, 订单号: {row[2]}")
            print(f"   姓名: {row[3]}, 状态: {row[4]}, 机构标题: {row[5]}")
        
        if results:
            # 设置前3个订单的chatnote为2
            test_orders = results[:3]
            order_ids = [row[0] for row in test_orders]
            
            for order_id in order_ids:
                cursor.execute("""
                    UPDATE ims_dayu_workorder_order 
                    SET chatnote = 2 
                    WHERE id = %s
                """, (order_id,))
            
            connection.commit()
            print(f"\n已将以下订单的chatnote设置为2:")
            for order in test_orders:
                print(f"  订单ID: {order[0]}, 机构: {order[5] or 'Unknown'}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 设置测试数据失败: {str(e)}")

def main():
    """主函数"""
    print("设置chatnote=2的测试数据")
    print("=" * 40)
    set_test_data()

if __name__ == "__main__":
    main()