/*
Navicat MySQL Data Transfer

Source Server         : yihe.skyoupin.com_3306
Source Server Version : 50650
Source Host           : yihe.skyoupin.com:3306
Source Database       : we7

Target Server Type    : MYSQL
Target Server Version : 50650
File Encoding         : 65001

Date: 2025-07-31 15:37:45
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for ims_dayu_workorder_data
-- ----------------------------
DROP TABLE IF EXISTS `ims_dayu_workorder_data`;
CREATE TABLE `ims_dayu_workorder_data` (
  `did` bigint(20) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL,
  `oid` int(11) NOT NULL,
  `fid` int(11) NOT NULL,
  `data` text NOT NULL,
  `displayorder` smallint(3) NOT NULL DEFAULT '0',
  `fid_name` varchar(20) DEFAULT NULL,
  <PERSON>IMAR<PERSON> KEY (`did`),
  KEY `oid` (`oid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3205788 DEFAULT CHARSET=utf8;
DROP TRIGGER IF EXISTS `update_pvalue_on_insert`;
DELIMITER ;;
CREATE TRIGGER `update_pvalue_on_insert` AFTER INSERT ON `ims_dayu_workorder_data` FOR EACH ROW BEGIN
    IF NEW.fid_name = '预约码' THEN
        UPDATE ims_dayu_workorder_order
        SET pvalue = NEW.data
        WHERE id = NEW.oid;
    END IF;
END
;;
DELIMITER ;
