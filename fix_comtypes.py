#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 comtypes 相关问题的脚本
"""

import os
import sys
import shutil
import subprocess
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def clear_comtypes_cache():
    """清理 comtypes 缓存"""
    try:
        import comtypes
        
        # 清理用户临时目录中的 comtypes 缓存
        temp_dirs = [
            os.path.join(os.path.expanduser("~"), "AppData", "Local", "Temp", "comtypes_cache"),
            os.path.join(os.environ.get('TEMP', ''), "comtypes_cache"),
            os.path.join(os.environ.get('TMP', ''), "comtypes_cache")
        ]
        
        for cache_dir in temp_dirs:
            if os.path.exists(cache_dir):
                try:
                    shutil.rmtree(cache_dir)
                    logging.info(f"已清理缓存目录: {cache_dir}")
                except Exception as e:
                    logging.warning(f"清理缓存目录失败 {cache_dir}: {str(e)}")
        
        # 清理 comtypes.gen 模块
        gen_dir = os.path.join(os.path.dirname(comtypes.__file__), "gen")
        if os.path.exists(gen_dir):
            for file in os.listdir(gen_dir):
                if file.endswith('.py') and file != '__init__.py':
                    try:
                        file_path = os.path.join(gen_dir, file)
                        os.remove(file_path)
                        logging.info(f"已删除生成的文件: {file}")
                    except Exception as e:
                        logging.warning(f"删除文件失败 {file}: {str(e)}")
        
        # 清理 __pycache__ 目录
        pycache_dirs = [
            os.path.join(gen_dir, "__pycache__"),
            os.path.join(os.path.dirname(comtypes.__file__), "__pycache__")
        ]
        
        for pycache_dir in pycache_dirs:
            if os.path.exists(pycache_dir):
                try:
                    shutil.rmtree(pycache_dir)
                    logging.info(f"已清理 __pycache__ 目录: {pycache_dir}")
                except Exception as e:
                    logging.warning(f"清理 __pycache__ 目录失败 {pycache_dir}: {str(e)}")
        
        return True
    except ImportError:
        logging.error("comtypes 模块未安装")
        return False
    except Exception as e:
        logging.error(f"清理 comtypes 缓存失败: {str(e)}")
        return False

def reinstall_comtypes():
    """重新安装 comtypes"""
    try:
        logging.info("开始重新安装 comtypes...")
        
        # 卸载 comtypes
        result = subprocess.run([sys.executable, "-m", "pip", "uninstall", "comtypes", "-y"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logging.info("已卸载 comtypes")
        else:
            logging.warning(f"卸载 comtypes 时出现警告: {result.stderr}")
        
        # 重新安装 comtypes
        result = subprocess.run([sys.executable, "-m", "pip", "install", "comtypes"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logging.info("已重新安装 comtypes")
            return True
        else:
            logging.error(f"重新安装 comtypes 失败: {result.stderr}")
            return False
            
    except Exception as e:
        logging.error(f"重新安装 comtypes 时出错: {str(e)}")
        return False

def test_comtypes():
    """测试 comtypes 是否正常工作"""
    try:
        import comtypes
        import comtypes.client
        logging.info("comtypes 导入成功")
        
        # 尝试创建一个简单的 COM 对象
        try:
            shell = comtypes.client.CreateObject("WScript.Shell")
            if shell:
                logging.info("COM 对象创建成功，comtypes 工作正常")
                return True
        except Exception as e:
            logging.warning(f"COM 对象创建失败，但 comtypes 基本功能正常: {str(e)}")
            return True
            
    except ImportError as e:
        logging.error(f"comtypes 导入失败: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"测试 comtypes 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    logging.info("开始修复 comtypes 相关问题...")
    
    # 步骤1: 清理缓存
    logging.info("步骤1: 清理 comtypes 缓存")
    if clear_comtypes_cache():
        logging.info("缓存清理完成")
    else:
        logging.warning("缓存清理失败")
    
    # 步骤2: 测试 comtypes
    logging.info("步骤2: 测试 comtypes")
    if test_comtypes():
        logging.info("comtypes 测试通过，修复完成")
        return True
    
    # 步骤3: 重新安装 comtypes
    logging.info("步骤3: 重新安装 comtypes")
    if reinstall_comtypes():
        logging.info("comtypes 重新安装完成")
        
        # 再次测试
        if test_comtypes():
            logging.info("comtypes 修复成功")
            return True
        else:
            logging.error("comtypes 修复失败")
            return False
    else:
        logging.error("comtypes 重新安装失败")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n修复完成！现在可以重新运行微信客服系统。")
    else:
        print("\n修复失败！请检查错误信息并手动处理。")
    
    input("按回车键退出...")
