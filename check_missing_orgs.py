#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查缺失微信映射的机构信息
"""

import pymysql
import configparser

def get_db_connection():
    """获取数据库连接"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        connection = pymysql.connect(
            host=config['mysql']['host'],
            port=int(config['mysql']['port']),
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            database=config['mysql']['database'],
            charset='utf8mb4'
        )
        return connection
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def check_missing_orgs():
    """检查缺失微信映射的机构"""
    try:
        connection = get_db_connection()
        if not connection:
            return
        
        cursor = connection.cursor()
        
        # 查询有待发送通知但没有微信映射的机构
        cursor.execute("""
            SELECT DISTINCT o.aid, a.title, COUNT(*) as pending_count
            FROM ims_dayu_workorder_order o
            LEFT JOIN ims_dayu_workorder_activity a ON o.aid = a.id
            LEFT JOIN ims_dayu_workorder_org_wechat w ON o.aid = w.aid AND w.status = 1
            WHERE o.chatnote = 2 
            AND (o.status = 0 OR o.status = 6)
            AND w.aid IS NULL
            GROUP BY o.aid, a.title
            ORDER BY pending_count DESC
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("发现以下机构有待发送通知但缺少微信映射：")
            print("-" * 60)
            for row in results:
                aid, title, count = row
                print(f"机构ID: {aid}")
                print(f"机构名称: {title or '未知'}")
                print(f"待发送通知数量: {count}")
                print("-" * 60)
        else:
            print("所有有待发送通知的机构都已配置微信映射")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"检查失败: {str(e)}")

if __name__ == "__main__":
    check_missing_orgs()
